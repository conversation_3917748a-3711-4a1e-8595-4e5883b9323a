/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "adc.h"
#include "dma.h"
#include "tim.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "arm_math.h"
#include "printf.h"
#include <math.h>
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */
#define FFT_SIZE 1024
#define SAMPLE_RATE 1000000  // 1MHz采样率 (45MHz APB1 / 45)
// ADC转换参数
#define ADC_RESOLUTION 4096.0f  // 12位ADC: 2^12 = 4096
#define ADC_VREF 3.3f          // 参考电压3.3V
#define ADC_TO_VOLTAGE(adc_val) ((float32_t)(adc_val) * ADC_VREF / ADC_RESOLUTION)
/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
uint16_t adcBuffer[FFT_SIZE];
float32_t fftInput[FFT_SIZE * 2];  // 复数输入，实部+虚部
float32_t fftOutput[FFT_SIZE];
arm_rfft_fast_instance_f32 fftInstance;
// 状态机定义
typedef enum {
    STATE_SAMPLING,     // 采样状态
    STATE_PROCESSING,   // FFT计算状态
    STATE_TRANSMITTING  // 数据发送状态
} SystemState;

volatile uint8_t adcComplete = 0;
volatile uint8_t outputDetailedData = 1;  // 控制是否输出详细数据，1=输出，0=仅摘要
SystemState currentState = STATE_SAMPLING;
volatile uint8_t transmitStage = 0;  // 发送阶段：0=原始数据，1=FFT结果，2=完成
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */
void Process_FFT(void);
void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef* hadc);
void StateMachine_Process(void);
void Transmit_Data_Chunk(void);
/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_USART3_UART_Init();
  MX_ADC1_Init();
  MX_TIM3_Init();
  /* USER CODE BEGIN 2 */
  // 初始化DSP库的FFT实例
  arm_rfft_fast_init_f32(&fftInstance, FFT_SIZE);
  
  // 启动定时器触发ADC采样
  HAL_TIM_Base_Start(&htim3);
  
  // 启动ADC DMA采样
  HAL_ADC_Start_DMA(&hadc1, (uint32_t*)adcBuffer, FFT_SIZE);
  
  printf("ADC FFT System Initialized\r\n");
  printf("Sampling Rate: %lu Hz\r\n", SAMPLE_RATE);
  printf("FFT Size: %d points\r\n", FFT_SIZE);
  printf("Frequency Resolution: %.2f Hz/bin\r\n", (float)SAMPLE_RATE / FFT_SIZE);
  printf("Max Detectable Freq: %lu Hz (Nyquist)\r\n", SAMPLE_RATE / 2);
  printf("Detailed Output: %s\r\n", outputDetailedData ? "ENABLED" : "DISABLED");
  printf("(Change outputDetailedData variable to toggle detail level)\r\n");
  printf("===========================================\r\n");
  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
    StateMachine_Process();
    HAL_Delay(10);  // 短暂延时避免CPU占用过高
  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_RCC_PWR_CLK_ENABLE();
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI;
  RCC_OscInitStruct.PLL.PLLM = 8;
  RCC_OscInitStruct.PLL.PLLN = 180;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = 4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Activate the Over-Drive mode
  */
  if (HAL_PWREx_EnableOverDrive() != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_5) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */
void Process_FFT(void) {
    uint32_t i;
    
    // 将ADC采样数据转换为电压值并准备FFT输入
    for (i = 0; i < FFT_SIZE; i++) {
        fftInput[i] = ADC_TO_VOLTAGE(adcBuffer[i]);
    }
    
    // 执行实数FFT
    arm_rfft_fast_f32(&fftInstance, fftInput, fftOutput, 0);
    
    // 计算幅度谱(只计算前半部分，因为实数FFT结果对称)  
    arm_cmplx_mag_f32(fftOutput, fftInput, FFT_SIZE / 2);
}

void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef* hadc) {
    if (hadc->Instance == ADC1) {
        adcComplete = 1;
        // 不在中断中发送数据，只设置标志
    }
}

void StateMachine_Process(void) {
    switch (currentState) {
        case STATE_SAMPLING:
            if (adcComplete) {
                adcComplete = 0;
                currentState = STATE_PROCESSING;
                printf("ADC DMA Complete - %d samples acquired\r\n", FFT_SIZE);
            }
            break;
            
        case STATE_PROCESSING:
            Process_FFT();
            currentState = STATE_TRANSMITTING;
            transmitStage = 0;
            break;
            
        case STATE_TRANSMITTING:
            Transmit_Data_Chunk();
            if (transmitStage >= 3) {  // 所有发送阶段完成
                currentState = STATE_SAMPLING;
                // 重新启动ADC采样
                HAL_ADC_Start_DMA(&hadc1, (uint32_t*)adcBuffer, FFT_SIZE);
            }
            break;
    }
}

void Transmit_Data_Chunk(void) {
    uint32_t i;
    float32_t frequency;
    
    switch (transmitStage) {
        case 0:  // 发送完整ADC原始数据
            printf("\r\n=== Complete ADC Raw Data (1024 samples) ===\r\n");
            printf("ADC Resolution: 12-bit (0-4095), Reference Voltage: %.1fV\r\n", ADC_VREF);
            for (i = 0; i < FFT_SIZE; i++) {
                if (i % 8 == 0) {
                    printf("\r\n[%4lu-%4lu]: ", i, (i + 7 < FFT_SIZE) ? i + 7 : FFT_SIZE - 1);
                }
                float32_t voltage = ADC_TO_VOLTAGE(adcBuffer[i]);
                printf("%4u(%.3fV) ", adcBuffer[i], voltage);
            }
            printf("\r\n=== End of Raw ADC Data ===\r\n");
            transmitStage = 1;
            break;
            
        case 1:  // 发送FFT分析结果
            {
                float32_t maxValue;
                uint32_t maxIndex;
                
                // 找到最大频率分量(跳过DC分量)
                arm_max_f32(&fftInput[1], (FFT_SIZE / 2) - 1, &maxValue, &maxIndex);
                maxIndex += 1;  // 调整索引
                frequency = (float32_t)maxIndex * SAMPLE_RATE / FFT_SIZE;
                
                printf("\r\n=== FFT Analysis Results ===\r\n");
                printf("Peak Frequency: %.2f Hz\r\n", frequency);
                printf("Peak Magnitude: %.6f V\r\n", maxValue);
                printf("Bin Index: %lu\r\n", maxIndex);
                printf("DC Component: %.6f V\r\n", fftInput[0]);
                
                // 输出完整的FFT幅度谱数据 (512个有效频率bin)
                printf("\r\n=== Complete FFT Magnitude Spectrum (512 bins) ===\r\n");
                printf("Units: Voltage (V)\r\n");
                for (i = 0; i < FFT_SIZE / 2; i++) {
                    if (i % 6 == 0) {
                        printf("\r\n[%3lu-%3lu]: ", i, (i + 5 < FFT_SIZE / 2) ? i + 5 : (FFT_SIZE / 2) - 1);
                    }
                    printf("%10.6f ", fftInput[i]);
                }
                printf("\r\n=== End of FFT Magnitude Data ===\r\n");
                
                // 输出频率对应表 (所有512个bin)
                printf("\r\n=== Complete Frequency Reference (All 512 bins) ===\r\n");
                printf("Format: Bin[Index]:Frequency(Hz)->Magnitude(V)\r\n");
                for (i = 0; i < FFT_SIZE / 2; i++) {
                    frequency = (float32_t)i * SAMPLE_RATE / FFT_SIZE;
                    if (i % 4 == 0) {
                        printf("\r\n");
                    }
                    printf("Bin%3lu:%8.2fHz->%8.6fV  ", i, frequency, fftInput[i]);
                }
                printf("\r\n=== End of Frequency Reference ===\r\n");
                
                transmitStage = 2;
            }
            break;
            
        case 2:  // 发送前10个峰值频率
            printf("\r\n=== Top 10 Peak Frequencies ===\r\n");
            
            // 创建临时缓冲区用于查找峰值
            {
                static float32_t tempBuffer[FFT_SIZE / 2];
                for (i = 0; i < FFT_SIZE / 2; i++) {
                    tempBuffer[i] = fftInput[i];
                }
                
                for (uint8_t rank = 1; rank <= 10; rank++) {
                    float32_t maxVal;
                    uint32_t maxIdx;
                    arm_max_f32(tempBuffer, FFT_SIZE / 2, &maxVal, &maxIdx);
                    frequency = (float32_t)maxIdx * SAMPLE_RATE / FFT_SIZE;
                    printf("%2d. Bin %3lu: %10.2f Hz -> Magnitude: %10.2f\r\n", rank, maxIdx, frequency, maxVal);
                    tempBuffer[maxIdx] = 0;
                }
            }
            
            printf("\r\n=== Processing Complete ===\r\n");
            printf("========================================\r\n\r\n");
            transmitStage = 3;  // 标记完成
            break;
    }
}
/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}
#ifdef USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
