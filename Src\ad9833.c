#include "ad9833.h"

void AD9833_Init(void)
{
    AD9833_FSC_H;
    AD9833_SCK_H;
}

void AD9833_Write(unsigned short word)
{
    short i;
    unsigned short w;
    AD9833_FSC_L;
    for(i=15;i>=0;i--)
    {
        w=word>>i;
        if(w&0x0001){AD9833_SDA_H;}
        else{AD9833_SDA_L;}
        AD9833_SCK_L;
        AD9833_SCK_H;
    }
    AD9833_FSC_H;
}

void AD9833_FreqSet(double freq)
{
    unsigned short freq_LSB,freq_MSB; 
    double freq_mid,freq_DATA;
    long int freq_hex;
    //calculate freq word
    freq_mid = 268435456/25;
    freq_DATA = freq;
    freq_DATA = freq_DATA/1000000;
    freq_DATA = freq_DATA*freq_mid;
    freq_hex = freq_DATA;
    //divide 14-bit words
    freq_LSB = freq_hex;
    freq_LSB = freq_LSB&0x3fff;
    freq_MSB = freq_hex>>14;
    freq_MSB = freq_MSB&0x3fff;
    //add register marker FREQ0
    freq_LSB = freq_LSB|0x4000;
    freq_MSB = freq_MSB|0x4000;

    AD9833_Write(0x2100);//ctrl word: consecutive write + chip reset
    AD9833_Write(freq_LSB);
    AD9833_Write(freq_MSB);
}
