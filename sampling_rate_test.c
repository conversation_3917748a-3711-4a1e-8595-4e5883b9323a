/* 
 * 采样率测试代码 - 用于验证1MHz采样率
 * 这个版本专门用于测试采样率，不进行FFT计算
 * 可以替换main.c中的相关部分进行测试
 */

// 在main.c中添加这些变量和函数来测试采样率

// 测试模式变量
volatile uint8_t testMode = 1;  // 1=采样率测试模式, 0=正常FFT模式
volatile uint32_t testSampleCount = 0;
volatile uint32_t testStartTime = 0;
volatile uint32_t testEndTime = 0;

// 简化的ADC完成回调 - 仅用于测试
void HAL_ADC_ConvCpltCallback_Test(ADC_HandleTypeDef* hadc) {
    if (hadc->Instance == ADC1 && testMode) {
        testSampleCount++;
        if (testSampleCount == 1) {
            testStartTime = HAL_GetTick();
        } else if (testSampleCount >= FFT_SIZE) {
            testEndTime = HAL_GetTick();
            adcComplete = 1;
        }
    }
}

// 简化的状态机 - 仅用于测试采样率
void StateMachine_Test(void) {
    static uint8_t testState = 0;
    
    switch (testState) {
        case 0:  // 启动测试
            testSampleCount = 0;
            adcComplete = 0;
            printf("Starting sampling rate test...\r\n");
            HAL_ADC_Start_DMA(&hadc1, (uint32_t*)adcBuffer, FFT_SIZE);
            testState = 1;
            break;
            
        case 1:  // 等待采样完成
            if (adcComplete) {
                HAL_ADC_Stop_DMA(&hadc1);
                
                uint32_t duration = testEndTime - testStartTime;
                float actualRate = (float)FFT_SIZE * 1000.0f / duration;
                float error = (actualRate - SAMPLE_RATE) * 100.0f / SAMPLE_RATE;
                
                printf("=== Sampling Rate Test Results ===\r\n");
                printf("Samples: %lu\r\n", testSampleCount);
                printf("Duration: %lu ms\r\n", duration);
                printf("Target Rate: %lu Hz\r\n", SAMPLE_RATE);
                printf("Actual Rate: %.2f Hz\r\n", actualRate);
                printf("Error: %.2f%%\r\n", error);
                printf("================================\r\n\r\n");
                
                // 显示前16个采样值
                printf("First 16 samples:\r\n");
                for (int i = 0; i < 16; i++) {
                    printf("%4u(%.3fV) ", adcBuffer[i], ADC_TO_VOLTAGE(adcBuffer[i]));
                    if ((i + 1) % 8 == 0) printf("\r\n");
                }
                printf("\r\n");
                
                testState = 2;
            }
            break;
            
        case 2:  // 等待重新开始
            HAL_Delay(2000);  // 等待2秒
            testState = 0;    // 重新开始测试
            break;
    }
}

// 使用说明：
// 1. 在main.c中添加 testMode 变量
// 2. 将 HAL_ADC_ConvCpltCallback 替换为 HAL_ADC_ConvCpltCallback_Test
// 3. 将主循环中的 StateMachine_Process() 替换为 StateMachine_Test()
// 4. 编译并运行，观察采样率测试结果

/*
修改main.c的步骤：

1. 在变量声明部分添加：
volatile uint8_t testMode = 1;
volatile uint32_t testSampleCount = 0;
volatile uint32_t testStartTime = 0;
volatile uint32_t testEndTime = 0;

2. 替换ADC回调函数：
void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef* hadc) {
    if (hadc->Instance == ADC1 && testMode) {
        testSampleCount++;
        if (testSampleCount == 1) {
            testStartTime = HAL_GetTick();
        } else if (testSampleCount >= FFT_SIZE) {
            testEndTime = HAL_GetTick();
            adcComplete = 1;
        }
    }
}

3. 在主循环中使用测试状态机：
while (1) {
    if (testMode) {
        StateMachine_Test();
    } else {
        StateMachine_Process();
    }
    HAL_Delay(1);
}
*/
